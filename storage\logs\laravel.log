[2025-07-21 08:17:59] local.ERROR: App\Repositories\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\Contracts\Interfaces\UserRepositoryInterface, App\Repositories\UserRepository given {"exception":"[object] (TypeError(code: 0): App\\Repositories\\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\\Contracts\\Interfaces\\UserRepositoryInterface, App\\Repositories\\UserRepository given at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\StudentRepository.php:27)
[stacktrace]
#0 [internal function]: App\\Repositories\\StudentRepository->__construct(Object(App\\Models\\Student), Object(App\\Repositories\\UserRepository))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Repositorie...')
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Repositorie...', Array, false)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(334): Illuminate\\Foundation\\Application->resolve('App\\\\Repositorie...', Array, false)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Contracts\\\\I...', Array, true)
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Contracts\\\\I...', Array)
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Contracts\\\\I...', Array)
#10 C:\\laragon\\www\\rawooh-v2\\app\\Providers\\AppServiceProvider.php(116): Illuminate\\Foundation\\Application->make('App\\\\Contracts\\\\I...')
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\St...', Array, true)
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\St...', Array)
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\St...', Array)
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\St...')
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#19 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#20 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#21 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#22 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#23 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(284): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#24 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#25 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#26 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#27 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#29 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#30 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 13)
#31 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#32 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#33 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#34 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#35 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#36 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-07-23 05:14:54] local.ERROR: App\Repositories\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\Contracts\Interfaces\UserRepositoryInterface, App\Repositories\UserRepository given {"exception":"[object] (TypeError(code: 0): App\\Repositories\\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\\Contracts\\Interfaces\\UserRepositoryInterface, App\\Repositories\\UserRepository given at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\StudentRepository.php:27)
[stacktrace]
#0 [internal function]: App\\Repositories\\StudentRepository->__construct(Object(App\\Models\\Student), Object(App\\Repositories\\UserRepository))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Repositorie...')
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Repositorie...', Array, false)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(334): Illuminate\\Foundation\\Application->resolve('App\\\\Repositorie...', Array, false)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Contracts\\\\I...', Array, true)
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Contracts\\\\I...', Array)
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Contracts\\\\I...', Array)
#10 C:\\laragon\\www\\rawooh-v2\\app\\Providers\\AppServiceProvider.php(116): Illuminate\\Foundation\\Application->make('App\\\\Contracts\\\\I...')
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\St...', Array, true)
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\St...', Array)
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\St...', Array)
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\St...')
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#19 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#20 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#21 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#22 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#23 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(284): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#24 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#25 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#26 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#27 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#29 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#30 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 13)
#31 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#32 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#33 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#34 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#35 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#36 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-07-23 05:57:44] local.ERROR: App\Repositories\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\Contracts\Interfaces\UserRepositoryInterface, App\Repositories\UserRepository given {"exception":"[object] (TypeError(code: 0): App\\Repositories\\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\\Contracts\\Interfaces\\UserRepositoryInterface, App\\Repositories\\UserRepository given at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\StudentRepository.php:27)
[stacktrace]
#0 [internal function]: App\\Repositories\\StudentRepository->__construct(Object(App\\Models\\Student), Object(App\\Repositories\\UserRepository))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Repositorie...')
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Repositorie...', Array, false)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(334): Illuminate\\Foundation\\Application->resolve('App\\\\Repositorie...', Array, false)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Contracts\\\\I...', Array, true)
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Contracts\\\\I...', Array)
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Contracts\\\\I...', Array)
#10 C:\\laragon\\www\\rawooh-v2\\app\\Providers\\AppServiceProvider.php(116): Illuminate\\Foundation\\Application->make('App\\\\Contracts\\\\I...')
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\St...', Array, true)
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\St...', Array)
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\St...', Array)
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\St...')
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#19 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#20 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#21 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#22 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#23 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(284): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#24 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#25 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#26 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#27 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#29 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#30 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 13)
#31 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#32 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#33 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#34 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#35 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#36 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-07-23 13:44:31] local.ERROR: App\Repositories\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\Contracts\Interfaces\UserRepositoryInterface, App\Repositories\UserRepository given {"exception":"[object] (TypeError(code: 0): App\\Repositories\\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\\Contracts\\Interfaces\\UserRepositoryInterface, App\\Repositories\\UserRepository given at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\StudentRepository.php:27)
[stacktrace]
#0 [internal function]: App\\Repositories\\StudentRepository->__construct(Object(App\\Models\\Student), Object(App\\Repositories\\UserRepository))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Repositorie...')
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Repositorie...', Array, false)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(334): Illuminate\\Foundation\\Application->resolve('App\\\\Repositorie...', Array, false)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Contracts\\\\I...', Array, true)
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Contracts\\\\I...', Array)
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Contracts\\\\I...', Array)
#10 C:\\laragon\\www\\rawooh-v2\\app\\Providers\\AppServiceProvider.php(116): Illuminate\\Foundation\\Application->make('App\\\\Contracts\\\\I...')
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\St...', Array, true)
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\St...', Array)
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\St...', Array)
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\St...')
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#19 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#20 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#21 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#22 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#23 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(284): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#24 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#25 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#26 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#27 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#29 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#30 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 13)
#31 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#32 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#33 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#34 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#35 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#36 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-07-23 13:46:30] local.ERROR: App\Repositories\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\Contracts\Interfaces\UserRepositoryInterface, App\Repositories\UserRepository given {"exception":"[object] (TypeError(code: 0): App\\Repositories\\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\\Contracts\\Interfaces\\UserRepositoryInterface, App\\Repositories\\UserRepository given at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\StudentRepository.php:27)
[stacktrace]
#0 [internal function]: App\\Repositories\\StudentRepository->__construct(Object(App\\Models\\Student), Object(App\\Repositories\\UserRepository))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Repositorie...')
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Repositorie...', Array, false)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(334): Illuminate\\Foundation\\Application->resolve('App\\\\Repositorie...', Array, false)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Contracts\\\\I...', Array, true)
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Contracts\\\\I...', Array)
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Contracts\\\\I...', Array)
#10 C:\\laragon\\www\\rawooh-v2\\app\\Providers\\AppServiceProvider.php(116): Illuminate\\Foundation\\Application->make('App\\\\Contracts\\\\I...')
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\St...', Array, true)
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\St...', Array)
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\St...', Array)
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\St...')
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#19 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#20 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#21 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#22 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#23 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(284): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#24 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#25 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#26 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#27 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#29 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#30 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 13)
#31 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#32 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#33 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#34 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#35 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#36 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-07-23 13:46:50] local.ERROR: App\Repositories\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\Contracts\Interfaces\UserRepositoryInterface, App\Repositories\UserRepository given {"exception":"[object] (TypeError(code: 0): App\\Repositories\\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\\Contracts\\Interfaces\\UserRepositoryInterface, App\\Repositories\\UserRepository given at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\StudentRepository.php:27)
[stacktrace]
#0 [internal function]: App\\Repositories\\StudentRepository->__construct(Object(App\\Models\\Student), Object(App\\Repositories\\UserRepository))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Repositorie...')
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Repositorie...', Array, false)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(334): Illuminate\\Foundation\\Application->resolve('App\\\\Repositorie...', Array, false)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Contracts\\\\I...', Array, true)
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Contracts\\\\I...', Array)
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Contracts\\\\I...', Array)
#10 C:\\laragon\\www\\rawooh-v2\\app\\Providers\\AppServiceProvider.php(116): Illuminate\\Foundation\\Application->make('App\\\\Contracts\\\\I...')
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\St...', Array, true)
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\St...', Array)
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\St...', Array)
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\St...')
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#19 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#20 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#21 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#22 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#23 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(284): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#24 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#25 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#26 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#27 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#29 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#30 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 13)
#31 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#32 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#33 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#34 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#35 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#36 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-07-24 06:21:42] local.ERROR: App\Repositories\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\Contracts\Interfaces\UserRepositoryInterface, App\Repositories\UserRepository given {"exception":"[object] (TypeError(code: 0): App\\Repositories\\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\\Contracts\\Interfaces\\UserRepositoryInterface, App\\Repositories\\UserRepository given at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\StudentRepository.php:27)
[stacktrace]
#0 [internal function]: App\\Repositories\\StudentRepository->__construct(Object(App\\Models\\Student), Object(App\\Repositories\\UserRepository))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Repositorie...')
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Repositorie...', Array, false)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(334): Illuminate\\Foundation\\Application->resolve('App\\\\Repositorie...', Array, false)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Contracts\\\\I...', Array, true)
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Contracts\\\\I...', Array)
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Contracts\\\\I...', Array)
#10 C:\\laragon\\www\\rawooh-v2\\app\\Providers\\AppServiceProvider.php(116): Illuminate\\Foundation\\Application->make('App\\\\Contracts\\\\I...')
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\St...', Array, true)
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\St...', Array)
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\St...', Array)
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\St...')
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#19 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#20 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#21 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#22 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#23 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(284): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#24 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#25 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#26 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#27 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#29 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#30 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 13)
#31 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#32 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#33 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#34 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#35 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#36 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-07-24 06:27:53] local.ERROR: App\Repositories\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\Contracts\Interfaces\UserRepositoryInterface, App\Repositories\UserRepository given {"exception":"[object] (TypeError(code: 0): App\\Repositories\\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\\Contracts\\Interfaces\\UserRepositoryInterface, App\\Repositories\\UserRepository given at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\StudentRepository.php:27)
[stacktrace]
#0 [internal function]: App\\Repositories\\StudentRepository->__construct(Object(App\\Models\\Student), Object(App\\Repositories\\UserRepository))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Repositorie...')
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Repositorie...', Array, false)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(334): Illuminate\\Foundation\\Application->resolve('App\\\\Repositorie...', Array, false)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Contracts\\\\I...', Array, true)
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Contracts\\\\I...', Array)
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Contracts\\\\I...', Array)
#10 C:\\laragon\\www\\rawooh-v2\\app\\Providers\\AppServiceProvider.php(116): Illuminate\\Foundation\\Application->make('App\\\\Contracts\\\\I...')
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\St...', Array, true)
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\St...', Array)
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\St...', Array)
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\St...')
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#19 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#20 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#21 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#22 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#23 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(284): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#24 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#25 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#26 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#27 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#29 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#30 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 13)
#31 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#32 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#33 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#34 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#35 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#36 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-07-24 07:10:35] local.ERROR: App\Repositories\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\Contracts\Interfaces\UserRepositoryInterface, App\Repositories\UserRepository given {"exception":"[object] (TypeError(code: 0): App\\Repositories\\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\\Contracts\\Interfaces\\UserRepositoryInterface, App\\Repositories\\UserRepository given at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\StudentRepository.php:27)
[stacktrace]
#0 [internal function]: App\\Repositories\\StudentRepository->__construct(Object(App\\Models\\Student), Object(App\\Repositories\\UserRepository))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Repositorie...')
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Repositorie...', Array, false)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(334): Illuminate\\Foundation\\Application->resolve('App\\\\Repositorie...', Array, false)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Contracts\\\\I...', Array, true)
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Contracts\\\\I...', Array)
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Contracts\\\\I...', Array)
#10 C:\\laragon\\www\\rawooh-v2\\app\\Providers\\AppServiceProvider.php(116): Illuminate\\Foundation\\Application->make('App\\\\Contracts\\\\I...')
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\St...', Array, true)
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\St...', Array)
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\St...', Array)
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\St...')
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#19 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#20 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#21 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#22 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#23 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(284): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#24 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#25 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#26 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#27 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#29 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#30 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 13)
#31 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#32 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#33 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#34 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#35 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#36 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-07-24 07:25:12] local.ERROR: App\Repositories\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\Contracts\Interfaces\UserRepositoryInterface, App\Repositories\UserRepository given {"exception":"[object] (TypeError(code: 0): App\\Repositories\\StudentRepository::__construct(): Argument #2 ($userRepository) must be of type App\\Contracts\\Interfaces\\UserRepositoryInterface, App\\Repositories\\UserRepository given at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\StudentRepository.php:27)
[stacktrace]
#0 [internal function]: App\\Repositories\\StudentRepository->__construct(Object(App\\Models\\Student), Object(App\\Repositories\\UserRepository))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Repositorie...')
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Repositorie...', Array, false)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(334): Illuminate\\Foundation\\Application->resolve('App\\\\Repositorie...', Array, false)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Container\\Container->Illuminate\\Container\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Contracts\\\\I...', Array, true)
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Contracts\\\\I...', Array)
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Contracts\\\\I...', Array)
#10 C:\\laragon\\www\\rawooh-v2\\app\\Providers\\AppServiceProvider.php(116): Illuminate\\Foundation\\Application->make('App\\\\Contracts\\\\I...')
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\St...', Array, true)
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\St...', Array)
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\St...', Array)
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\St...')
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#19 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#20 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#21 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#22 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#23 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(284): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#24 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#25 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#26 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#27 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#29 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#30 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 13)
#31 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#32 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#33 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#34 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#35 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#36 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-07-24 08:43:32] local.ERROR: Class App\Repositories\UserRepository contains 4 abstract methods and must therefore be declared abstract or implement the remaining methods (App\Contracts\Interfaces\UserRepositoryInterface::getUserById, App\Contracts\Interfaces\UserRepositoryInterface::createUser, App\Contracts\Interfaces\UserRepositoryInterface::updateUser, ...) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Class App\\Repositories\\UserRepository contains 4 abstract methods and must therefore be declared abstract or implement the remaining methods (App\\Contracts\\Interfaces\\UserRepositoryInterface::getUserById, App\\Contracts\\Interfaces\\UserRepositoryInterface::createUser, App\\Contracts\\Interfaces\\UserRepositoryInterface::updateUser, ...) at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\UserRepository.php:12)
[stacktrace]
#0 {main}
"} 
[2025-07-24 08:44:18] local.ERROR: Class App\Repositories\UserRepository contains 3 abstract methods and must therefore be declared abstract or implement the remaining methods (App\Contracts\Interfaces\UserRepositoryInterface::updateUser, App\Contracts\Interfaces\UserRepositoryInterface::deleteUser, App\Contracts\Interfaces\UserRepositoryInterface::getAllUsers) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Class App\\Repositories\\UserRepository contains 3 abstract methods and must therefore be declared abstract or implement the remaining methods (App\\Contracts\\Interfaces\\UserRepositoryInterface::updateUser, App\\Contracts\\Interfaces\\UserRepositoryInterface::deleteUser, App\\Contracts\\Interfaces\\UserRepositoryInterface::getAllUsers) at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\UserRepository.php:12)
[stacktrace]
#0 {main}
"} 
[2025-07-24 08:53:36] local.ERROR: Class App\Repositories\UserRepository contains 5 abstract methods and must therefore be declared abstract or implement the remaining methods (App\Contracts\Interfaces\UserRepositoryInterface::getAllUsers, App\Contracts\Interfaces\UserRepositoryInterface::getUserById, App\Contracts\Interfaces\UserRepositoryInterface::createUser, ...) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Class App\\Repositories\\UserRepository contains 5 abstract methods and must therefore be declared abstract or implement the remaining methods (App\\Contracts\\Interfaces\\UserRepositoryInterface::getAllUsers, App\\Contracts\\Interfaces\\UserRepositoryInterface::getUserById, App\\Contracts\\Interfaces\\UserRepositoryInterface::createUser, ...) at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\UserRepository.php:12)
[stacktrace]
#0 {main}
"} 
[2025-07-24 08:53:44] local.ERROR: Class App\Repositories\UserRepository contains 5 abstract methods and must therefore be declared abstract or implement the remaining methods (App\Contracts\Interfaces\UserRepositoryInterface::getAllUsers, App\Contracts\Interfaces\UserRepositoryInterface::getUserById, App\Contracts\Interfaces\UserRepositoryInterface::createUser, ...) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Class App\\Repositories\\UserRepository contains 5 abstract methods and must therefore be declared abstract or implement the remaining methods (App\\Contracts\\Interfaces\\UserRepositoryInterface::getAllUsers, App\\Contracts\\Interfaces\\UserRepositoryInterface::getUserById, App\\Contracts\\Interfaces\\UserRepositoryInterface::createUser, ...) at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\UserRepository.php:12)
[stacktrace]
#0 {main}
"} 
[2025-07-24 08:53:51] local.ERROR: Class App\Repositories\UserRepository contains 5 abstract methods and must therefore be declared abstract or implement the remaining methods (App\Contracts\Interfaces\UserRepositoryInterface::getAllUsers, App\Contracts\Interfaces\UserRepositoryInterface::getUserById, App\Contracts\Interfaces\UserRepositoryInterface::createUser, ...) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Class App\\Repositories\\UserRepository contains 5 abstract methods and must therefore be declared abstract or implement the remaining methods (App\\Contracts\\Interfaces\\UserRepositoryInterface::getAllUsers, App\\Contracts\\Interfaces\\UserRepositoryInterface::getUserById, App\\Contracts\\Interfaces\\UserRepositoryInterface::createUser, ...) at C:\\laragon\\www\\rawooh-v2\\app\\Repositories\\UserRepository.php:12)
[stacktrace]
#0 {main}
"} 
[2025-07-24 08:55:01] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php  = app(Ap...', false)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode(' = app(App\\\\Repo...', true)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode(' = app(App\\\\Repo...', true)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute(' = app(App\\\\Repo...')
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-07-26 04:32:48] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php echo 'Tes...', false)
#2 C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('echo 'Testing U...', true)
#4 C:\\laragon\\www\\rawooh-v2\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('echo 'Testing U...', true)
#5 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo 'Testing U...')
#6 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\laragon\\www\\rawooh-v2\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\laragon\\www\\rawooh-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\laragon\\www\\rawooh-v2\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
